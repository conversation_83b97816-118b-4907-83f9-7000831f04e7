from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFormLayout, QTextEdit, QHeaderView, QMessageBox,
                            QDialog, QComboBox, QGroupBox, QDateEdit, QDoubleSpinBox,
                            QSizePolicy, QFrame, QTextBrowser, QMenu, QAction, QFileDialog)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTime
from PyQt5.QtGui import QIcon, QFont, QTextDocument, QColor
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

from database import Expense, Supplier
from utils import (show_error_message, show_info_message, show_confirmation_message,
                    qdate_to_datetime, datetime_to_qdate, format_currency)
import datetime

from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox,
                                StyledTable, StyledLabel)

class ExpenseDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل مصروف"""

    def __init__(self, parent=None, expense=None, session=None):
        super().__init__(parent)
        self.expense = expense
        self.session = session
        self.init_ui()

    def init_ui(self):
        # إعداد نافذة الحوار
        if self.expense:
            self.setWindowTitle("تعديل مصروف")
        else:
            self.setWindowTitle("إضافة مصروف جديد")

        self.setMinimumWidth(400)

        # إنشاء النموذج
        form_layout = QFormLayout()

        # حقل العنوان
        self.title_edit = QLineEdit()
        self.title_edit.setStyleSheet(UnifiedStyles.get_input_style())
        if self.expense:
            self.title_edit.setText(self.expense.title)
        form_layout.addRow("العنوان:", self.title_edit)

        # حقل المبلغ
        self.amount_edit = QDoubleSpinBox()
        self.amount_edit.setRange(0, 1000000)
        self.amount_edit.setDecimals(0)  # بدون كسور عشرية
        self.amount_edit.setSingleStep(100)
        if self.expense:
            self.amount_edit.setValue(self.expense.amount)
        form_layout.addRow("المبلغ:", self.amount_edit)

        # حقل التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        if self.expense and self.expense.date:
            self.date_edit.setDate(datetime_to_qdate(self.expense.date))
        form_layout.addRow("التاريخ:", self.date_edit)

        # حقل الفئة
        self.category_edit = QComboBox()
        categories = ["مواد خام", "أدوات", "صيانة", "إيجار", "مرافق", "رواتب", "تسويق", "نقل", "أخرى"]
        self.category_edit.addItems(categories)
        if self.expense and self.expense.category:
            index = self.category_edit.findText(self.expense.category)
            if index >= 0:
                self.category_edit.setCurrentIndex(index)
        form_layout.addRow("الفئة:", self.category_edit)

        # حقل المورد
        self.supplier_combo = QComboBox()
        self.supplier_combo.addItem("-- بدون مورد --", None)

        # إضافة الموردين من قاعدة البيانات
        if self.session:
            suppliers = self.session.query(Supplier).all()
            for supplier in suppliers:
                self.supplier_combo.addItem(supplier.name, supplier.id)

        # تحديد المورد الحالي إذا كان موجودًا
        if self.expense and self.expense.supplier_id:
            index = self.supplier_combo.findData(self.expense.supplier_id)
            if index >= 0:
                self.supplier_combo.setCurrentIndex(index)

        form_layout.addRow("المورد:", self.supplier_combo)

        # حقل الملاحظات
        self.notes_edit = QTextEdit()
        if self.expense and self.expense.notes:
            self.notes_edit.setText(self.expense.notes)
        form_layout.addRow("ملاحظات:", self.notes_edit)

        # أزرار الحفظ والإلغاء
        button_layout = QHBoxLayout()

        self.save_button = StyledButton("💾 حفظ", "success", "normal")
        self.save_button.clicked.connect(self.accept)

        self.cancel_button = StyledButton("❌ إلغاء", "secondary", "normal")
        self.cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(self.save_button.button)
        button_layout.addWidget(self.cancel_button.button)

        # تجميع التخطيط النهائي
        main_layout = QVBoxLayout()
        main_layout.addLayout(form_layout)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

    def get_data(self):
        """الحصول على بيانات المصروف من النموذج"""
        title = self.title_edit.text().strip()
        amount = self.amount_edit.value()
        date = qdate_to_datetime(self.date_edit.date())
        category = self.category_edit.currentText()
        supplier_id = self.supplier_combo.currentData()
        notes = self.notes_edit.toPlainText().strip()

        # التحقق من صحة البيانات
        if not title:
            show_error_message("خطأ", "يجب إدخال عنوان المصروف")
            return None

        if amount <= 0:
            show_error_message("خطأ", "يجب أن يكون المبلغ أكبر من صفر")
            return None

        return {
            'title': title,
            'amount': amount,
            'date': date,
            'category': category,
            'supplier_id': supplier_id,
            'notes': notes
        }

class ExpensesWidget(QWidget):
    """واجهة إدارة المصروفات"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()
        self.refresh_data()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مع تقليل المساحات لاستغلال الجدول
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("💰 إدارة المصروفات المتطورة - نظام شامل ومتقدم لإدارة المصروفات مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للموردين
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالعنوان، الفئة، المورد أو الملاحظات...")
        self.search_edit.textChanged.connect(self.filter_expenses)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 3px 10px rgba(96, 165, 250, 0.2);
            }
        """)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                padding: 8px;
                font-size: 22px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)
        search_button.clicked.connect(self.filter_expenses)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة بألوان احترافية مطابقة للفواتير
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة مطابقة للفواتير
        self.create_custom_status_filter()

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter_frame, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول المصروفات المتطور والمحسن
        self.create_advanced_expenses_table()

        main_layout.addWidget(top_frame)
        main_layout.addWidget(self.expenses_table, 1)  # إعطاء الجدول أولوية في التمدد

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للموردين
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للموردين

        # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة مطابقة للفواتير
        self.add_button = QPushButton("➕ إضافة مصروف")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.clicked.connect(self.add_expense)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')  # أزرق سماوي متطور مطابق للفواتير
        self.edit_button.clicked.connect(self.edit_expense)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_expense)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التحديث بسيط بدون قائمة منسدلة مطابق للفواتير
        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة مطابقة للفواتير
        self.view_button = QPushButton("👁️ عرض التفاصيل ▼")
        self.style_advanced_button(self.view_button, 'indigo', has_menu=True)  # بنفسجي للتفاصيل
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # ربط زر العرض بالوظيفة الأساسية
        self.view_button.clicked.connect(self.view_expense)

        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)  # لون متسق مع نظام الألوان
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.report_button = QPushButton("📋 التقارير")
        self.style_advanced_button(self.report_button, 'cyan')  # سيان مميز للتقارير
        self.report_button.clicked.connect(self.generate_expenses_report)
        self.report_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة للتصدير مطابقة لقسم العملاء
        from ui.unified_styles import UnifiedStyles
        export_menu = QMenu(self)
        export_menu.setStyleSheet(UnifiedStyles.get_menu_style('info', 'normal'))

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(self.export_to_excel)
        export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(self.export_to_pdf)
        export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(csv_action)

        json_action = QAction("🔗 تصدير إلى JSON", self)
        json_action.triggered.connect(self.export_to_json)
        export_menu.addAction(json_action)

        self.export_button.setMenu(export_menu)

        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')  # وردي للإحصائيات
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إجمالي المصروفات مطور ليتشابه مع الفواتير مع حفظ المقاسات والخط الداخلي
        self.total_label = QLabel("إجمالي المصروفات: 0.00")
        self.total_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #7f1d1d,
                    stop:0.1 #991b1b,
                    stop:0.9 #b91c1c,
                    stop:1 #ef4444);
                border: 5px solid #ef4444;
                border-radius: 20px;
                min-height: 34px;
                max-height: 38px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           2px 2px 4px rgba(0, 0, 0, 0.7),
                           1px 1px 2px rgba(0, 0, 0, 0.5);
                box-shadow: 0 8px 20px rgba(239, 68, 68, 0.6),
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(239, 68, 68, 0.6),
                           0 0 40px rgba(255, 255, 255, 0.1);
                letter-spacing: 0.5px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        """)
        self.total_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.report_button)
        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.total_label)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع التخطيط النهائي
        main_layout.addWidget(bottom_frame)

        self.setLayout(main_layout)

    def create_advanced_expenses_table(self):
        """إنشاء جدول المصروفات المتطور والمحسن مطابق للموردين"""
        styled_table = StyledTable()
        self.expenses_table = styled_table.table
        self.expenses_table.setColumnCount(7)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🆔 الرقم التسلسلي",
            "💰 العنوان",
            "💵 المبلغ",
            "📅 التاريخ",
            "🏷️ الفئة",
            "🏢 المورد",
            "📋 ملاحظات"
        ]
        self.expenses_table.setHorizontalHeaderLabels(headers)

        # تحسين عرض الأعمدة
        header = self.expenses_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # الرقم
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # العنوان
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # المبلغ
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # التاريخ
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # الفئة
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # المورد
        header.setSectionResizeMode(6, QHeaderView.Stretch)  # الملاحظات

        # تحديد عرض الأعمدة الثابتة
        self.expenses_table.setColumnWidth(0, 80)   # الرقم
        self.expenses_table.setColumnWidth(2, 120)  # المبلغ
        self.expenses_table.setColumnWidth(3, 120)  # التاريخ
        self.expenses_table.setColumnWidth(4, 100)  # الفئة

        self.expenses_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.expenses_table.setSelectionMode(QTableWidget.SingleSelection)
        self.expenses_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.expenses_table.setAlternatingRowColors(True)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للفواتير
        self.expenses_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للفواتير
        header = self.expenses_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للفواتير
        self.expenses_table.verticalHeader().setDefaultSectionSize(45)
        self.expenses_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        # إضافة العلامة المائية للجدول مطابقة للفواتير
        self.add_watermark_to_expenses_table()

    def add_watermark_to_expenses_table(self):
        """إضافة علامة مائية لجدول المصروفات مطابقة للفواتير"""
        try:
            # إنشاء العلامة المائية مثل الفواتير
            watermark = QLabel("Smart Finish", self.expenses_table)
            watermark.setAlignment(Qt.AlignCenter)

            # تنسيق العلامة المائية مكبرة أكثر للتوحيد مع الباقي
            font = QFont("Arial", 150, QFont.Bold)
            watermark.setFont(font)

            # تطبيق تصميم محسن للعلامة المائية - أكثر وضوحاً لكن لا يؤثر على البيانات
            watermark.setStyleSheet("""
                QLabel {
                    color: rgba(71, 85, 105, 0.35);
                    background: transparent;
                    border: none;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
                    font-weight: bold;
                }
            """)

            # إصلاح مشكلة التفاعل - العلامة المائية لا تتداخل مع النقرات
            watermark.setAttribute(Qt.WA_TransparentForMouseEvents, True)
            watermark.setEnabled(False)  # تعطيل التفاعل مع العلامة المائية
            watermark.lower()  # وضع العلامة المائية في الخلف

            # تحسين إضافي للشفافية والوضوح
            watermark.setWindowOpacity(0.6)  # شفافية محسنة

            # تحديد موضع وحجم العلامة المائية مع تحسين الموضع
            def update_watermark_geometry():
                if self.expenses_table.isVisible():
                    rect = self.expenses_table.rect()
                    # تحسين الموضع ليكون في المنتصف مع مساحة أفضل
                    watermark.setGeometry(rect.x() + 50, rect.y() + 100,
                                        rect.width() - 100, rect.height() - 200)
                    watermark.lower()  # وضع العلامة المائية في الخلف دائماً
                    watermark.setAttribute(Qt.WA_TransparentForMouseEvents, True)

            # ربط تحديث الموضع بتغيير حجم الجدول
            original_resize_event = self.expenses_table.resizeEvent
            def custom_resize_event(event):
                original_resize_event(event)
                update_watermark_geometry()
            self.expenses_table.resizeEvent = custom_resize_event

            # تحديث أولي للموضع
            update_watermark_geometry()

        except Exception as e:
            print(f"خطأ في إضافة العلامة المائية: {str(e)}")

    def refresh_data(self):
        """تحديث بيانات المصروفات في الجدول"""
        # الحصول على جميع المصروفات من قاعدة البيانات
        expenses = self.session.query(Expense).all()
        self.populate_table(expenses)
        self.update_total(expenses)

    def populate_table(self, expenses):
        """ملء جدول المصروفات بالبيانات مع أيقونات متطورة مطابقة للفواتير"""
        try:
            # تعطيل تحديث الجدول مؤقتًا لتحسين الأداء
            self.expenses_table.setUpdatesEnabled(False)

            # مسح الجدول
            self.expenses_table.setRowCount(0)

            # إضافة الصفوف مع تنسيق محسن وأيقونات متطورة
            for row, expense in enumerate(expenses):
                try:
                    self.expenses_table.insertRow(row)

                    # 1. الرقم التسلسلي مع تنسيق متطور وأيقونة
                    id_item = QTableWidgetItem(f"🆔 #{str(expense.id).zfill(4)}")
                    id_item.setTextAlignment(Qt.AlignCenter)
                    id_item.setFont(QFont("Consolas", 12, QFont.Bold))
                    id_item.setForeground(QColor("#0f172a"))
                    id_item.setToolTip(f"🆔 الرقم التسلسلي: {expense.id}")
                    self.expenses_table.setItem(row, 0, id_item)

                    # 2. عنوان المصروف مع أيقونة
                    title_item = QTableWidgetItem(f"📝 {expense.title}")
                    title_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    title_item.setForeground(QColor("#1e40af"))
                    title_item.setToolTip(f"📝 عنوان المصروف: {expense.title}\n💡 انقر نقرتين للتعديل")
                    self.expenses_table.setItem(row, 1, title_item)

                    # 3. المبلغ مع تنسيق مالي متطور
                    try:
                        if expense.amount and expense.amount > 0:
                            amount_formatted = f"{int(expense.amount):,}".replace(',', '٬')
                            amount_display = f"💰 {amount_formatted} جنيه"
                            amount_color = QColor("#dc2626")  # أحمر للمصروفات
                        else:
                            amount_display = "💰 0 جنيه"
                            amount_color = QColor("#6b7280")
                    except Exception:
                        amount_display = "💰 0 جنيه"
                        amount_color = QColor("#6b7280")
                    amount_item = QTableWidgetItem(amount_display)
                    amount_item.setTextAlignment(Qt.AlignCenter)
                    amount_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    amount_item.setForeground(amount_color)
                    amount_item.setToolTip(f"💰 مبلغ المصروف: {amount_display}")
                    self.expenses_table.setItem(row, 2, amount_item)

                    # 4. التاريخ مع تنسيق أنيق
                    try:
                        if expense.date:
                            date_formatted = expense.date.strftime("%d/%m/%Y")
                            date_display = f"📅 {date_formatted}"
                        else:
                            date_display = "📅 غير محدد"
                    except Exception:
                        date_display = "📅 غير محدد"
                    date_item = QTableWidgetItem(date_display)
                    date_item.setTextAlignment(Qt.AlignCenter)
                    date_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                    date_item.setForeground(QColor("#374151"))
                    date_item.setToolTip(f"📅 تاريخ المصروف: {date_display}")
                    self.expenses_table.setItem(row, 3, date_item)

                    # 5. الفئة مع أيقونة مناسبة
                    category = expense.category or "غير محدد"
                    category_icons = {
                        "مكتب": "🏢",
                        "سفر": "✈️",
                        "طعام": "🍽️",
                        "مواصلات": "🚗",
                        "اتصالات": "📞",
                        "كهرباء": "⚡",
                        "مياه": "💧",
                        "إيجار": "🏠",
                        "صيانة": "🔧",
                        "تسويق": "📢",
                        "أخرى": "📦"
                    }
                    category_icon = category_icons.get(category, "📂")
                    category_item = QTableWidgetItem(f"{category_icon} {category}")
                    category_item.setTextAlignment(Qt.AlignCenter)
                    category_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                    category_item.setForeground(QColor("#7c3aed"))
                    category_item.setToolTip(f"{category_icon} فئة المصروف: {category}")
                    self.expenses_table.setItem(row, 4, category_item)

                    # 6. المورد مع أيقونة
                    try:
                        supplier_name = expense.supplier.name if expense.supplier else "غير محدد"
                    except Exception:
                        supplier_name = "غير محدد"
                    supplier_item = QTableWidgetItem(f"🏪 {supplier_name}")
                    supplier_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    supplier_item.setForeground(QColor("#065f46"))
                    supplier_item.setToolTip(f"🏪 المورد: {supplier_name}\n💡 انقر نقرتين لعرض تفاصيل المورد")
                    self.expenses_table.setItem(row, 5, supplier_item)

                    # 7. الملاحظات مع أيقونة
                    notes = expense.notes or "لا توجد ملاحظات"
                    notes_display = f"📋 {notes[:30]}..." if len(notes) > 30 else f"📋 {notes}"
                    notes_item = QTableWidgetItem(notes_display)
                    notes_item.setFont(QFont("Segoe UI", 10, QFont.Normal))
                    notes_item.setForeground(QColor("#6b7280"))
                    notes_item.setToolTip(f"📋 الملاحظات: {notes}")
                    self.expenses_table.setItem(row, 6, notes_item)

                except Exception as row_error:
                    # تجاهل الصف الذي به خطأ والاستمرار في العملية
                    print(f"خطأ في الصف {row}: {str(row_error)}")
                    continue

            # إعادة تمكين تحديث الجدول
            self.expenses_table.setUpdatesEnabled(True)

        except Exception as e:
            # إعادة تمكين تحديث الجدول في حالة حدوث خطأ
            self.expenses_table.setUpdatesEnabled(True)
            show_error_message("خطأ", f"حدث خطأ أثناء تحديث جدول المصروفات: {str(e)}")

    def update_total(self, expenses):
        """تحديث إجمالي المصروفات"""
        total = sum(expense.amount for expense in expenses)
        self.total_label.setText(f"إجمالي المصروفات: {format_currency(total)}")

    def filter_expenses(self):
        """تصفية المصروفات بناءً على نص البحث والفئة"""
        search_text = self.search_edit.text().strip().lower()
        category = getattr(self, 'current_filter_value', 'all')

        # بناء الاستعلام
        query = self.session.query(Expense)

        # تطبيق تصفية النص
        if search_text:
            query = query.filter(
                Expense.title.like(f"%{search_text}%") |
                Expense.notes.like(f"%{search_text}%")
            )

        # تطبيق تصفية الفئة
        if category and category != "all":
            query = query.filter(Expense.category == category)

        # تنفيذ الاستعلام
        expenses = query.all()

        # تحديث الجدول والإجمالي
        self.populate_table(expenses)
        self.update_total(expenses)

    def add_expense(self):
        """إضافة مصروف جديد"""
        dialog = ExpenseDialog(self, session=self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # إنشاء مصروف جديد في قاعدة البيانات
                expense = Expense(**data)
                self.session.add(expense)
                self.session.commit()

                show_info_message("تم", "تمت إضافة المصروف بنجاح")
                self.refresh_data()

    def edit_expense(self):
        """تعديل بيانات مصروف"""
        selected_row = self.expenses_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مصروف من القائمة")
            return

        expense_id = int(self.expenses_table.item(selected_row, 0).text())
        expense = self.session.query(Expense).get(expense_id)

        if not expense:
            show_error_message("خطأ", "لم يتم العثور على المصروف")
            return

        dialog = ExpenseDialog(self, expense, self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # تحديث بيانات المصروف
                for key, value in data.items():
                    setattr(expense, key, value)

                self.session.commit()
                show_info_message("تم", "تم تحديث بيانات المصروف بنجاح")
                self.refresh_data()

    def delete_expense(self):
        """حذف مصروف"""
        selected_row = self.expenses_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مصروف من القائمة")
            return

        expense_id = int(self.expenses_table.item(selected_row, 0).text())
        expense = self.session.query(Expense).get(expense_id)

        if not expense:
            show_error_message("خطأ", "لم يتم العثور على المصروف")
            return

        # طلب تأكيد الحذف
        if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف المصروف '{expense.title}'؟"):
            self.session.delete(expense)
            self.session.commit()
            show_info_message("تم", "تم حذف المصروف بنجاح")
            self.refresh_data()

    def view_expense(self):
        """عرض تفاصيل مصروف"""
        selected_row = self.expenses_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مصروف من القائمة")
            return

        expense_id = int(self.expenses_table.item(selected_row, 0).text())
        expense = self.session.query(Expense).get(expense_id)

        if not expense:
            show_error_message("خطأ", "لم يتم العثور على المصروف")
            return

        # عرض تفاصيل المصروف
        date_str = expense.date.strftime("%Y-%m-%d") if expense.date else "غير محدد"
        supplier_name = expense.supplier.name if expense.supplier else "غير محدد"

        details = f"""
📋 تفاصيل المصروف
═══════════════════════════════════════════════════════════════════════════════

🆔 الرقم: {expense.id}
📝 العنوان: {expense.title}
💰 المبلغ: {int(expense.amount):,} جنيه
📅 التاريخ: {date_str}
🏷️ الفئة: {expense.category or 'غير محدد'}
🏭 المورد: {supplier_name}
📝 الملاحظات: {expense.notes or 'لا توجد ملاحظات'}

═══════════════════════════════════════════════════════════════════════════════
        """

        show_info_message("تفاصيل المصروف", details)

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة مطابقة للفواتير"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            QFrame:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
            QFrame:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """)

        # إنشاء تخطيط أفقي للإطار
        filter_layout = QHBoxLayout(self.status_filter_frame)
        filter_layout.setContentsMargins(5, 0, 5, 0)
        filter_layout.setSpacing(8)

        # سهم يسار (مشابه للسهم الأيمن)
        self.left_arrow = QPushButton("▼")
        self.left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # النص الحالي
        self.current_filter_label = QLabel("جميع الفئات")
        self.current_filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص أفقياً وعمودياً
        self.current_filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                font-family: 'Segoe UI', Arial, sans-serif;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
            }
        """)

        # زر القائمة (سهم يمين)
        self.filter_menu_button = QPushButton("▼")
        self.filter_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # إضافة العناصر للتخطيط - سهم يسار، النص في المنتصف، سهم يمين
        filter_layout.addWidget(self.left_arrow, 0)
        filter_layout.addWidget(self.current_filter_label, 1)
        filter_layout.addWidget(self.filter_menu_button, 0)

        # إنشاء القائمة المنسدلة
        self.filter_menu = QMenu(self)
        self.filter_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 2px solid rgba(96, 165, 250, 0.3);
                padding: 12px 0px;
                border-radius: 15px;
                margin: 3px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                font-family: 'Courier New', 'Consolas', monospace;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                font-family: 'Courier New', 'Consolas', monospace;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                font-family: 'Courier New', 'Consolas', monospace;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
        """)

        # إضافة خيارات التصفية
        filter_options = [
            ("جميع الفئات", "all"),
            ("مواد خام", "مواد خام"),
            ("أدوات", "أدوات"),
            ("صيانة", "صيانة"),
            ("إيجار", "إيجار"),
            ("مرافق", "مرافق"),
            ("رواتب", "رواتب"),
            ("تسويق", "تسويق"),
            ("نقل", "نقل"),
            ("أخرى", "أخرى")
        ]

        for text, value in filter_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_filter(v, t))
            self.filter_menu.addAction(action)

        # ربط الأزرار بالقائمة
        self.filter_menu_button.clicked.connect(self.show_filter_menu)
        self.left_arrow.clicked.connect(self.show_filter_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event
        self.current_filter_label.mousePressEvent = self.frame_mouse_press_event

    def show_filter_menu(self):
        """عرض القائمة المنسدلة للتصفية"""
        # تحديد موقع القائمة تحت الإطار
        button_rect = self.status_filter_frame.geometry()
        menu_pos = self.status_filter_frame.mapToGlobal(button_rect.bottomLeft())
        menu_pos.setY(menu_pos.y() + 5)  # إضافة مسافة صغيرة

        self.filter_menu.exec_(menu_pos)

    def frame_mouse_press_event(self, event):
        """التعامل مع الضغط على الإطار"""
        if event.button() == Qt.LeftButton:
            self.show_filter_menu()

    def set_filter(self, filter_value, filter_text):
        """تعيين التصفية وتحديث النص"""
        self.current_filter_label.setText(filter_text)
        self.current_filter_value = filter_value
        self.filter_expenses()

    def generate_expenses_report(self):
        """إنشاء تقرير المصروفات"""
        try:
            expenses = self.session.query(Expense).all()

            if not expenses:
                show_info_message("تقرير المصروفات", "لا توجد مصروفات لإنشاء التقرير")
                return

            # حساب الإحصائيات
            total_expenses = sum(expense.amount for expense in expenses)
            categories = {}
            for expense in expenses:
                category = expense.category or 'غير محدد'
                if category not in categories:
                    categories[category] = {'count': 0, 'total': 0}
                categories[category]['count'] += 1
                categories[category]['total'] += expense.amount

            # إنشاء محتوى التقرير
            report_content = f"""
═══════════════════════════════════════════════════════════════════════════════
                                📋 تقرير مفصل للمصروفات
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الإنشاء: {QTime.currentTime().toString('hh:mm:ss')}

📊 ملخص عام:
─────────────────────────────────────────────────────────────────────────────
💸 إجمالي المصروفات: {int(total_expenses):,} جنيه
📝 عدد المصروفات: {len(expenses)}
🏷️ عدد الفئات: {len(categories)}

📈 تفصيل حسب الفئات:
─────────────────────────────────────────────────────────────────────────────
"""

            for category, data in categories.items():
                percentage = (data['total'] / total_expenses) * 100
                report_content += f"• {category}: {int(data['total']):,} جنيه ({data['count']} مصروف) - {percentage:.1f}%\n"

            report_content += f"""
─────────────────────────────────────────────────────────────────────────────

📋 تفاصيل المصروفات:
═══════════════════════════════════════════════════════════════════════════════
"""

            for expense in expenses:
                date_str = expense.date.strftime("%Y-%m-%d") if expense.date else "غير محدد"
                supplier_name = expense.supplier.name if expense.supplier else "غير محدد"
                report_content += f"""
🔸 {expense.title}
   📅 التاريخ: {date_str}
   💰 المبلغ: {int(expense.amount):,} جنيه
   🏷️ الفئة: {expense.category or 'غير محدد'}
   🏭 المورد: {supplier_name}
   📝 الملاحظات: {expense.notes or 'لا توجد ملاحظات'}
   ─────────────────────────────────────────────────────────────────────────────
"""

            # عرض التقرير
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextBrowser, QPushButton, QHBoxLayout

            dialog = QDialog(self)
            dialog.setWindowTitle("📋 تقرير المصروفات")
            dialog.setModal(True)
            dialog.resize(800, 600)

            layout = QVBoxLayout()

            text_browser = QTextBrowser()
            text_browser.setPlainText(report_content)
            text_browser.setStyleSheet("""
                QTextBrowser {
                    font-family: 'Segoe UI', Arial, sans-serif;
                    font-size: 11px;
                    line-height: 1.4;
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            layout.addWidget(text_browser)

            # أزرار الحفظ والطباعة
            buttons_layout = QHBoxLayout()

            save_button = QPushButton("💾 حفظ التقرير")
            save_button.clicked.connect(lambda: self.save_report_to_file(report_content, "تقرير_المصروفات"))
            buttons_layout.addWidget(save_button)

            print_button = QPushButton("🖨️ طباعة")
            print_button.clicked.connect(lambda: self.print_report(text_browser))
            buttons_layout.addWidget(print_button)

            close_button = QPushButton("❌ إغلاق")
            close_button.clicked.connect(dialog.close)
            buttons_layout.addWidget(close_button)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}")

    def show_statistics(self):
        """عرض إحصائيات المصروفات"""
        try:
            expenses = self.session.query(Expense).all()

            if not expenses:
                show_info_message("إحصائيات المصروفات", "لا توجد مصروفات لعرض الإحصائيات")
                return

            # حساب الإحصائيات
            total_expenses = sum(expense.amount for expense in expenses)
            avg_expense = total_expenses / len(expenses)

            # إحصائيات حسب الفئة
            categories = {}
            for expense in expenses:
                category = expense.category or 'غير محدد'
                if category not in categories:
                    categories[category] = {'count': 0, 'total': 0}
                categories[category]['count'] += 1
                categories[category]['total'] += expense.amount

            # إحصائيات حسب الشهر
            from collections import defaultdict
            monthly_stats = defaultdict(float)
            for expense in expenses:
                if expense.date:
                    month_key = expense.date.strftime("%Y-%m")
                    monthly_stats[month_key] += expense.amount

            # أكبر وأصغر مصروف
            max_expense = max(expenses, key=lambda x: x.amount)
            min_expense = min(expenses, key=lambda x: x.amount)

            # إنشاء محتوى الإحصائيات
            stats_content = f"""
═══════════════════════════════════════════════════════════════════════════════
                                📊 إحصائيات المصروفات
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ الإحصائيات: {QDate.currentDate().toString('yyyy-MM-dd')}

📈 الإحصائيات العامة:
─────────────────────────────────────────────────────────────────────────────
💸 إجمالي المصروفات: {int(total_expenses):,} جنيه
📝 عدد المصروفات: {len(expenses)}
📊 متوسط المصروف: {int(avg_expense):,} جنيه
🔺 أكبر مصروف: {int(max_expense.amount):,} جنيه ({max_expense.title})
🔻 أصغر مصروف: {int(min_expense.amount):,} جنيه ({min_expense.title})

🏷️ الإحصائيات حسب الفئة:
─────────────────────────────────────────────────────────────────────────────
"""

            for category, data in sorted(categories.items(), key=lambda x: x[1]['total'], reverse=True):
                percentage = (data['total'] / total_expenses) * 100
                avg_category = data['total'] / data['count']
                stats_content += f"• {category}:\n"
                stats_content += f"  💰 المجموع: {int(data['total']):,} جنيه ({percentage:.1f}%)\n"
                stats_content += f"  📝 العدد: {data['count']} مصروف\n"
                stats_content += f"  📊 المتوسط: {int(avg_category):,} جنيه\n\n"

            if monthly_stats:
                stats_content += """
📅 الإحصائيات الشهرية:
─────────────────────────────────────────────────────────────────────────────
"""
                for month, total in sorted(monthly_stats.items(), reverse=True):
                    stats_content += f"• {month}: {int(total):,} جنيه\n"

            # عرض الإحصائيات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextBrowser, QPushButton

            dialog = QDialog(self)
            dialog.setWindowTitle("📊 إحصائيات المصروفات")
            dialog.setModal(True)
            dialog.resize(700, 500)

            layout = QVBoxLayout()

            text_browser = QTextBrowser()
            text_browser.setPlainText(stats_content)
            text_browser.setStyleSheet("""
                QTextBrowser {
                    font-family: 'Segoe UI', Arial, sans-serif;
                    font-size: 12px;
                    line-height: 1.5;
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            layout.addWidget(text_browser)

            close_button = QPushButton("❌ إغلاق")
            close_button.clicked.connect(dialog.close)
            layout.addWidget(close_button)

            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def export_to_excel(self):
        """تصدير المصروفات إلى Excel"""
        try:
            import csv
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel", "المصروفات.csv", "CSV Files (*.csv)"
            )

            if file_path:
                expenses = self.session.query(Expense).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رؤوس الأعمدة
                    writer.writerow(['الرقم', 'العنوان', 'المبلغ', 'التاريخ', 'الفئة', 'المورد', 'الملاحظات'])

                    # كتابة البيانات
                    for expense in expenses:
                        date_str = expense.date.strftime("%Y-%m-%d") if expense.date else ""
                        supplier_name = expense.supplier.name if expense.supplier else ""
                        writer.writerow([
                            expense.id,
                            expense.title,
                            expense.amount,
                            date_str,
                            expense.category or "",
                            supplier_name,
                            expense.notes or ""
                        ])

                show_info_message("تم", f"تم تصدير المصروفات بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def export_to_pdf(self):
        """تصدير المصروفات إلى PDF"""
        try:

            expenses = self.session.query(Expense).all()

            if not expenses:
                show_info_message("تصدير PDF", "لا توجد مصروفات للتصدير")
                return

            # إنشاء محتوى HTML
            html_content = f"""
            <html dir="rtl">
            <head>
                <meta charset="utf-8">
                <title>تقرير المصروفات</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #e74c3c; text-align: center; }}
                    table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                    th {{ background-color: #f2f2f2; }}
                    .total {{ font-weight: bold; background-color: #e8f5e8; }}
                </style>
            </head>
            <body>
                <h1>📋 تقرير المصروفات</h1>
                <p><strong>تاريخ التقرير:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>
                <p><strong>وقت الإنشاء:</strong> {QTime.currentTime().toString('hh:mm:ss')}</p>

                <table>
                    <tr>
                        <th>الرقم</th>
                        <th>العنوان</th>
                        <th>المبلغ</th>
                        <th>التاريخ</th>
                        <th>الفئة</th>
                        <th>المورد</th>
                        <th>الملاحظات</th>
                    </tr>
            """

            total_amount = 0
            for expense in expenses:
                date_str = expense.date.strftime("%Y-%m-%d") if expense.date else ""
                supplier_name = expense.supplier.name if expense.supplier else ""
                total_amount += expense.amount

                html_content += f"""
                    <tr>
                        <td>{expense.id}</td>
                        <td>{expense.title}</td>
                        <td>{int(expense.amount):,} جنيه</td>
                        <td>{date_str}</td>
                        <td>{expense.category or ""}</td>
                        <td>{supplier_name}</td>
                        <td>{expense.notes or ""}</td>
                    </tr>
                """

            html_content += f"""
                    <tr class="total">
                        <td colspan="2"><strong>الإجمالي</strong></td>
                        <td><strong>{int(total_amount):,} جنيه</strong></td>
                        <td colspan="4"></td>
                    </tr>
                </table>
            </body>
            </html>
            """

            # حفظ ملف PDF مباشرة
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المصروفات", "تقرير_المصروفات.pdf", "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء طابعة PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

                # إنشاء مستند وطباعته إلى PDF
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                show_info_message("تم", f"تم تصدير المصروفات إلى PDF بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير PDF: {str(e)}")

    def print_expenses(self):
        """طباعة المصروفات مباشرة"""
        try:
            expenses = self.session.query(Expense).all()

            if not expenses:
                show_info_message("طباعة", "لا توجد مصروفات للطباعة")
                return

            # إنشاء محتوى HTML للطباعة
            html_content = f"""
            <html dir="rtl">
            <head>
                <meta charset="utf-8">
                <title>تقرير المصروفات</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #e74c3c; text-align: center; }}
                    table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                    th {{ background-color: #f2f2f2; }}
                    .total {{ font-weight: bold; background-color: #e8f5e8; }}
                </style>
            </head>
            <body>
                <h1>📋 تقرير المصروفات</h1>
                <p><strong>تاريخ التقرير:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>
                <p><strong>وقت الإنشاء:</strong> {QTime.currentTime().toString('hh:mm:ss')}</p>

                <table>
                    <tr>
                        <th>الرقم</th>
                        <th>العنوان</th>
                        <th>المبلغ</th>
                        <th>التاريخ</th>
                        <th>الفئة</th>
                        <th>المورد</th>
                        <th>الملاحظات</th>
                    </tr>
            """

            total_amount = 0
            for expense in expenses:
                date_str = expense.date.strftime("%Y-%m-%d") if expense.date else ""
                supplier_name = expense.supplier.name if expense.supplier else ""
                total_amount += expense.amount

                html_content += f"""
                    <tr>
                        <td>{expense.id}</td>
                        <td>{expense.title}</td>
                        <td>{int(expense.amount):,} جنيه</td>
                        <td>{date_str}</td>
                        <td>{expense.category or ""}</td>
                        <td>{supplier_name}</td>
                        <td>{expense.notes or ""}</td>
                    </tr>
                """

            html_content += f"""
                    <tr class="total">
                        <td colspan="2"><strong>الإجمالي</strong></td>
                        <td><strong>{int(total_amount):,} جنيه</strong></td>
                        <td colspan="4"></td>
                    </tr>
                </table>
            </body>
            </html>
            """

            # إنشاء مستند وطباعته
            document = QTextDocument()
            document.setHtml(html_content)

            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)

            if print_dialog.exec_() == QPrintDialog.Accepted:
                document.print_(printer)
                show_info_message("تم", "تم إرسال المصروفات للطباعة بنجاح")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في الطباعة: {str(e)}")

    def export_to_csv(self):
        """تصدير المصروفات إلى CSV"""
        self.export_to_excel()  # نفس الوظيفة

    def export_to_json(self):
        """تصدير المصروفات إلى JSON"""
        try:
            import json
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف JSON", "المصروفات.json", "JSON Files (*.json)"
            )

            if file_path:
                expenses = self.session.query(Expense).all()

                expenses_data = []
                for expense in expenses:
                    expense_data = {
                        'id': expense.id,
                        'title': expense.title,
                        'amount': expense.amount,
                        'date': expense.date.strftime("%Y-%m-%d") if expense.date else "",
                        'category': expense.category or "",
                        'supplier_name': expense.supplier.name if expense.supplier else "",
                        'supplier_id': expense.supplier_id,
                        'notes': expense.notes or ""
                    }
                    expenses_data.append(expense_data)

                export_data = {
                    "export_info": {
                        "export_date": QDate.currentDate().toString('yyyy-MM-dd'),
                        "export_time": QTime.currentTime().toString('hh:mm:ss'),
                        "total_expenses": len(expenses_data),
                        "total_amount": sum(expense.amount for expense in expenses),
                        "exported_by": "نظام إدارة المصروفات"
                    },
                    "expenses": expenses_data
                }

                with open(file_path, 'w', encoding='utf-8') as jsonfile:
                    json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)

                show_info_message("تم", f"تم تصدير المصروفات بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def save_report_to_file(self, content, filename):
        """حفظ التقرير إلى ملف نصي"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير", f"{filename}.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                show_info_message("تم", f"تم حفظ التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في حفظ التقرير: {str(e)}")

    def print_report(self, text_browser):
        """طباعة التقرير"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)

            if print_dialog.exec_() == QPrintDialog.Accepted:
                text_browser.print_(printer)
                show_info_message("تم", "تم إرسال التقرير للطباعة بنجاح")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في الطباعة: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0369a1', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.6)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#0d9488',
                    'hover_start': '#0d9488', 'hover_mid': '#14b8a6', 'hover_end': '#2dd4bf', 'hover_bottom': '#5eead4',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.6)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#0891b2',
                    'hover_start': '#0891b2', 'hover_mid': '#06b6d4', 'hover_end': '#22d3ee', 'hover_bottom': '#67e8f9',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.6)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#be185d',
                    'hover_start': '#be185d', 'hover_mid': '#ec4899', 'hover_end': '#f472b6', 'hover_bottom': '#f9a8d4',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.6)'
                },
                'indigo': {
                    'bg_start': '#312e81', 'bg_mid': '#3730a3', 'bg_end': '#4338ca', 'bg_bottom': '#4f46e5',
                    'hover_start': '#4f46e5', 'hover_mid': '#6366f1', 'hover_end': '#818cf8', 'hover_bottom': '#a5b4fc',
                    'hover_border': '#6366f1', 'pressed_start': '#312e81', 'pressed_mid': '#3730a3',
                    'pressed_end': '#4338ca', 'pressed_bottom': '#4f46e5', 'pressed_border': '#4338ca',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.6)'
                }
            }

            # الحصول على نظام الألوان المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور مطابق للفواتير
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.3 {color_scheme['bg_mid']},
                        stop:0.7 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 3px solid {color_scheme['border']};
                    border-radius: 20px;
                    padding: 12px 20px;
                    font-size: 16px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 34px;
                    max-height: 38px;
                    min-width: 120px;
                    text-align: center;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                               2px 2px 4px rgba(0, 0, 0, 0.7),
                               1px 1px 2px rgba(0, 0, 0, 0.5);
                    box-shadow: 0 8px 20px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 25px rgba(255, 255, 255, 0.1);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.3 {color_scheme['hover_mid']},
                        stop:0.7 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 3px solid {color_scheme['hover_border']};
                    transform: translateY(-2px);
                    box-shadow: 0 12px 25px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.5),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.3),
                               0 0 30px rgba(255, 255, 255, 0.2);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.3 {color_scheme['pressed_mid']},
                        stop:0.7 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 3px solid {color_scheme['pressed_border']};
                    transform: translateY(1px);
                    box-shadow: 0 4px 10px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(0, 0, 0, 0.3),
                               inset 0 -2px 0 rgba(255, 255, 255, 0.2);
                }}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {str(e)}")
            # تطبيق تصميم افتراضي في حالة الخطأ
            button.setStyleSheet("""
                QPushButton {
                    background: #3b82f6;
                    color: white;
                    border: 3px solid #1d4ed8;
                    border-radius: 20px;
                    padding: 12px 20px;
                    font-size: 16px;
                    font-weight: bold;
                    min-height: 34px;
                    max-height: 38px;
                }
                QPushButton:hover {
                    background: #2563eb;
                }
                QPushButton:pressed {
                    background: #1d4ed8;
                }
            """)
